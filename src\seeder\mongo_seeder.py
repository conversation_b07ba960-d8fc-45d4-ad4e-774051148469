import json
import os
import sys
from bson import ObjectId
from datetime import datetime
from common.aria_helper.boto3_utils import get_secret
from common.aria_helper.mongo_utils import Mongo

# Environment setup
environment = os.environ.get('ENV', 'dev')
database_name = os.environ.get('DATABASE_NAME', 'hennessy_db')

# MongoDB setup
mongo_client = Mongo(get_secret(environment + '-mongodb_uri', return_json=False))
mongo_client.select_db_and_collection(database_name, 'py1')  # single document per app_id

files_to_seed = [
    ("new_collection_data.json", "validation_rules"),
    ("hennessy.prompts.json", "prompts"),
    ("hennessy.Workitem.json", "workitems"),
    ("hennessy.Wordblock.json", "wordblocks")
]

def convert_extended_json(obj):
    if isinstance(obj, dict):
        if "$oid" in obj:
            return ObjectId(obj["$oid"])
        if "$date" in obj:
            return datetime.fromisoformat(obj["$date"].replace("Z", "+00:00"))
        return {k: convert_extended_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_extended_json(item) for item in obj]
    else:
        return obj

def seed(app_id: str):
    container = {
        "app_id": app_id,
        "created_at": datetime.now(datetime.timezone.utc),
        "source_data": {}
    }

    for file_name, tag in files_to_seed:
        if not os.path.exists(file_name):
            print(f"❌ Missing: {file_name}")
            continue

        with open(file_name, 'r', encoding='utf-8') as f:
            raw = json.load(f)
            parsed = convert_extended_json(raw)
            container["source_data"][tag] = parsed

    # Use mongo_client methods instead of direct collection access
    mongo_client.col.delete_many({"app_id": app_id})  # optional overwrite
    mongo_client.insert_one(container)
    print(f"✅ Inserted single grouped record for app_id: {app_id}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("❌ Usage: python seeder.py <app_id>")
        sys.exit(1)

    app_id_arg = sys.argv[1]
    seed(app_id_arg)
